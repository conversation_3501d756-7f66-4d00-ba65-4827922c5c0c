package com.bm.atool.ui;

import android.os.Bundle;
import android.os.Handler;
import android.text.TextUtils;
import android.util.Log;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.Button;
import android.widget.EditText;
import android.widget.TextView;
import android.widget.Toast;

import com.bm.atool.R;
import com.bm.atool.autojs.SimpleAutoJsManager;

/**
 * 简化的AutoJs脚本执行Fragment
 * 提供基本的JS脚本编辑和执行功能
 */
public class SimpleAutoJsFragment extends BaseFragment {
    private static final String TAG = "SimpleAutoJsFragment";
    
    private EditText etScriptContent;
    private EditText etScriptName;
    private Button btnExecuteScript;
    private Button btnStopAllScripts;
    private Button btnCheckStatus;
    private TextView tvScriptStatus;
    private TextView tvRunningScripts;
    
    private SimpleAutoJsManager autoJsManager;
    private Handler uiHandler;
    
    public SimpleAutoJsFragment() {
        super();
        this.setTitle("JS脚本执行器");
    }
    
    @Override
    public View onCreateView(LayoutInflater inflater, ViewGroup container, Bundle savedInstanceState) {
        View view = inflater.inflate(R.layout.fragment_simple_autojs, container, false);
        
        initViews(view);
        initAutoJs();
        setupClickListeners();
        updateUI();
        
        return view;
    }
    
    private void initViews(View view) {
        etScriptContent = view.findViewById(R.id.etScriptContent);
        etScriptName = view.findViewById(R.id.etScriptName);
        btnExecuteScript = view.findViewById(R.id.btnExecuteScript);
        btnStopAllScripts = view.findViewById(R.id.btnStopAllScripts);
        btnCheckStatus = view.findViewById(R.id.btnCheckStatus);
        tvScriptStatus = view.findViewById(R.id.tvScriptStatus);
        tvRunningScripts = view.findViewById(R.id.tvRunningScripts);
        
        uiHandler = new Handler();
        
        // 设置默认脚本内容
        setDefaultScript();
    }
    
    private void initAutoJs() {
        autoJsManager = SimpleAutoJsManager.getInstance();
        if (!autoJsManager.isInitialized()) {
            Log.e(TAG, "SimpleAutoJsManager not initialized");
            showToast("JS引擎未初始化，请重启应用");
        }
    }
    
    private void setupClickListeners() {
        btnExecuteScript.setOnClickListener(v -> executeScript());
        btnStopAllScripts.setOnClickListener(v -> stopAllScripts());
        btnCheckStatus.setOnClickListener(v -> checkStatus());
    }
    
    private void setDefaultScript() {
        String defaultScript = "// 简化版JavaScript脚本测试（带步骤Toast）\n" +
                "function step(title, fn) {\n" +
                "  try {\n" +
                "    toast('开始: ' + title);\n" +
                "    console.log('[START] ' + title);\n" +
                "    var res = fn();\n" +
                "    toast('成功: ' + title);\n" +
                "    console.log('[OK] ' + title);\n" +
                "    return res;\n" +
                "  } catch (e) {\n" +
                "    console.log('[FAIL] ' + title + ' -> ' + (e && e.message ? e.message : e));\n" +
                "    toast('失败: ' + title + ' -> ' + (e && e.message ? e.message : e));\n" +
                "    throw e;\n" +
                "  }\n" +
                "}\n\n" +
                "console.log('脚本开始执行！');\n" +
                "toast('脚本开始执行！');\n\n" +
                "step('获取设备信息', function(){\n" +
                "  var deviceInfo = { width: device.width, height: device.height, brand: device.brand, model: device.model };\n" +
                "  console.log('设备信息: ' + JSON.stringify(deviceInfo));\n" +
                "});\n\n" +
                "step('等待2秒', function(){ sleep(2000); });\n\n" +
                "var currentApp = step('获取当前应用', function(){\n" +
                "  var pkg = currentPackage();\n" +
                "  console.log('当前应用包名: ' + pkg);\n" +
                "  return pkg;\n" +
                "});\n" +
                "toast('当前应用: ' + currentApp);\n\n" +
                "console.log('脚本执行完成');\n" +
                "toast('脚本执行完成！');\n" +
                "return '脚本执行成功';";

        etScriptContent.setText(defaultScript);
        etScriptName.setText("测试脚本");
    }
    
    private void executeScript() {
        String scriptContent = etScriptContent.getText().toString().trim();
        String scriptName = etScriptName.getText().toString().trim();
        
        if (TextUtils.isEmpty(scriptContent)) {
            showToast("请输入脚本内容");
            return;
        }
        
        if (TextUtils.isEmpty(scriptName)) {
            scriptName = "未命名脚本";
        }
        
        if (!autoJsManager.isInitialized()) {
            showToast("JS引擎未初始化");
            return;
        }
        
        try {
            // 执行脚本
            String scriptId = autoJsManager.executeScript(scriptContent, scriptName, 
                new SimpleAutoJsManager.ScriptExecutionCallback() {
                    @Override
                    public void onStart(String scriptName) {
                        uiHandler.post(() -> {
                            tvScriptStatus.setText("脚本正在执行: " + scriptName);
                            updateRunningScriptsCount();
                        });
                    }
                    
                    @Override
                    public void onSuccess(String scriptName, String result) {
                        uiHandler.post(() -> {
                            tvScriptStatus.setText("脚本执行成功: " + scriptName + " - " + result);
                            updateRunningScriptsCount();
                            showToast("脚本执行成功");
                        });
                    }
                    
                    @Override
                    public void onError(String scriptName, String error) {
                        uiHandler.post(() -> {
                            tvScriptStatus.setText("脚本执行失败: " + scriptName + " - " + error);
                            updateRunningScriptsCount();
                            showToast("脚本执行失败: " + error);
                        });
                    }
                });
            
            if (scriptId != null) {
                Log.d(TAG, "Script execution started: " + scriptName + " (ID: " + scriptId + ")");
                showToast("脚本开始执行");
                updateRunningScriptsCount();
            } else {
                showToast("脚本执行失败");
                tvScriptStatus.setText("脚本执行失败");
            }
            
        } catch (Exception e) {
            Log.e(TAG, "Error executing script", e);
            showToast("脚本执行出错: " + e.getMessage());
            tvScriptStatus.setText("脚本执行出错: " + e.getMessage());
        }
    }
    
    private void stopAllScripts() {
        try {
            autoJsManager.stopAllScripts();
            showToast("已停止所有脚本");
            tvScriptStatus.setText("已停止所有脚本");
            updateRunningScriptsCount();
        } catch (Exception e) {
            Log.e(TAG, "Error stopping scripts", e);
            showToast("停止脚本失败: " + e.getMessage());
        }
    }
    
    private void checkStatus() {
        if (!autoJsManager.isInitialized()) {
            showToast("JS引擎未初始化");
            return;
        }
        
        int runningCount = autoJsManager.getRunningScriptCount();
        String status = "JS引擎状态: 正常\n运行中的脚本: " + runningCount;
        
        showToast("JS引擎运行正常");
        tvScriptStatus.setText(status);
        updateRunningScriptsCount();
    }
    
    private void updateRunningScriptsCount() {
        try {
            int count = autoJsManager.getRunningScriptCount();
            tvRunningScripts.setText("运行中的脚本: " + count);
        } catch (Exception e) {
            Log.e(TAG, "Error getting running script count", e);
            tvRunningScripts.setText("运行中的脚本: 未知");
        }
    }
    
    private void updateUI() {
        updateRunningScriptsCount();
        
        if (autoJsManager.isInitialized()) {
            tvScriptStatus.setText("JS引擎已就绪");
        } else {
            tvScriptStatus.setText("JS引擎未初始化");
        }
    }
    
    private void showToast(String message) {
        if (getContext() != null) {
            Toast.makeText(getContext(), message, Toast.LENGTH_SHORT).show();
        }
    }
    
    @Override
    public void onResume() {
        super.onResume();
        updateUI();
    }
    
    @Override
    public int getIconResourceId() {
        return R.drawable.ic_code;
    }
}
