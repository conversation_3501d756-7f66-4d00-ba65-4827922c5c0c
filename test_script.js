// 测试脚本 - 验证toast函数是否正常工作
console.log("=== 简化版AutoJs测试脚本开始 ===");
console.log("");
console.log("脚本名称: 简化版AutoJs功能测试");
console.log("执行时间: " + new Date().toLocaleString());

// 测试toast函数
try {
    console.log("正在测试toast函数...");
    toast("Hello, AutoJs!");
    console.log("toast函数测试成功！");
} catch (e) {
    console.log("toast函数测试失败: " + e.message);
}

// 测试sleep函数
try {
    console.log("正在测试sleep函数...");
    sleep(1000); // 等待1秒
    console.log("sleep函数测试成功！");
} catch (e) {
    console.log("sleep函数测试失败: " + e.message);
}

// 测试device对象
try {
    console.log("正在测试device对象...");
    console.log("设备宽度: " + device.width);
    console.log("设备高度: " + device.height);
    console.log("设备品牌: " + device.brand);
    console.log("device对象测试成功！");
} catch (e) {
    console.log("device对象测试失败: " + e.message);
}

// 测试currentPackage函数
try {
    console.log("正在测试currentPackage函数...");
    var pkg = currentPackage();
    console.log("当前应用包名: " + pkg);
    console.log("currentPackage函数测试成功！");
} catch (e) {
    console.log("currentPackage函数测试失败: " + e.message);
}

console.log("");
console.log("=== 测试脚本执行完成 ===");
