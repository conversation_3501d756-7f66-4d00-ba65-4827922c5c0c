# AutoJs6 集成说明

## 概述

本项目集成了一个简化版的JavaScript自动化执行引擎，基于Android WebView实现，提供基本的脚本执行功能。

## 功能特性

### 已实现功能
- ✅ 基本JavaScript脚本执行
- ✅ 日志输出 (`console.log()`)
- ✅ Toast消息显示 (`toast()`)
- ✅ 延时等待 (`sleep()`)
- ✅ 设备信息获取 (`device` 对象)
- ✅ 当前应用包名获取 (`currentPackage()`)
- ✅ 脚本执行状态监控
- ✅ 多脚本并发执行支持
- ✅ 脚本停止功能

### 暂未实现功能
- ❌ 无障碍服务集成
- ❌ 屏幕点击/滑动操作
- ❌ 图像识别和OCR
- ❌ 文件系统操作
- ❌ 网络请求功能
- ❌ 复杂的UI自动化

## 使用方法

### 1. 启动应用
打开Android Tool应用，切换到"JS脚本执行器"标签页。

### 2. 编写脚本
在脚本内容输入框中编写JavaScript代码，例如：

```javascript
// 基本示例
console.log("Hello AutoJs!");
toast("脚本开始执行");

// 获取设备信息
console.log("设备宽度: " + device.width);
console.log("设备高度: " + device.height);

// 等待2秒
sleep(2000);

// 获取当前应用
var app = currentPackage();
console.log("当前应用: " + app);
toast("当前应用: " + app);

return "脚本执行完成";
```

### 3. 执行脚本
1. 输入脚本名称（可选）
2. 点击"执行脚本"按钮
3. 观察执行状态和日志输出

### 4. 管理脚本
- 使用"停止所有脚本"按钮终止正在运行的脚本
- 使用"检查状态"按钮查看JS引擎状态
- 查看"运行中的脚本"计数器了解当前执行情况

## 可用API

### 日志输出
```javascript
console.log("消息内容");
```

### Toast显示
```javascript
toast("要显示的消息");
```

### 延时等待
```javascript
sleep(毫秒数); // 例如: sleep(2000) 等待2秒
```

### 设备信息
```javascript
device.width    // 屏幕宽度
device.height   // 屏幕高度
device.brand    // 设备品牌
device.model    // 设备型号
```

### 应用信息
```javascript
var packageName = currentPackage(); // 获取当前应用包名
```

### 自动化服务状态
```javascript
auto.service // 无障碍服务状态（当前总是false）
```

## 技术实现

### 架构设计
```
SimpleAutoJsManager (管理器)
    ↓
SimpleJsEngine (执行引擎)
    ↓
WebView (JavaScript运行环境)
    ↓
JavaScript Interface (API桥接)
```

### 核心组件

1. **SimpleAutoJsManager**: 脚本管理器，负责脚本的创建、执行、停止和状态管理
2. **SimpleJsEngine**: JavaScript执行引擎，基于WebView实现
3. **SimpleAutoJsFragment**: 用户界面，提供脚本编辑和执行功能
4. **JavaScript Interface**: 提供JavaScript与Android原生功能的桥接

### 执行流程
1. 用户在界面输入JavaScript代码
2. SimpleAutoJsManager创建SimpleJsEngine实例
3. SimpleJsEngine将脚本包装并注入到WebView中执行
4. JavaScript通过Interface调用Android原生功能
5. 执行结果通过回调返回给用户界面

## 测试脚本

项目包含一个完整的测试脚本 `test_scripts/simple_js_test.js`，包含以下测试项目：

1. 基本信息输出
2. Toast消息显示
3. 延时等待功能
4. 设备信息获取
5. 应用信息获取
6. JavaScript基础功能
7. 数学计算
8. 循环控制
9. 函数定义和调用
10. 错误处理
11. 执行结果返回

## 限制和注意事项

### 功能限制
- 这是一个简化版本，主要用于学习和基本测试
- 不支持复杂的UI自动化操作
- 不包含无障碍服务功能
- 不支持文件系统和网络操作

### 性能考虑
- 基于WebView实现，性能相对较低
- 不适合执行大量计算密集型任务
- 建议脚本执行时间不超过几分钟

### 安全注意
- 脚本在WebView沙箱环境中执行
- 无法访问敏感系统功能
- 相对安全，但仍需谨慎使用

## 扩展开发

如需扩展功能，可以：

1. 在`JsInterface`类中添加新的`@JavascriptInterface`方法
2. 在WebView初始化时注入更多JavaScript API
3. 集成真正的AutoJs6库以获得完整功能

## 故障排除

### 常见问题

1. **脚本无法执行**
   - 检查JS引擎是否初始化成功
   - 查看日志输出是否有错误信息
   - 重启应用重新初始化

2. **Toast不显示**
   - 确保应用有显示悬浮窗权限
   - 检查设备是否开启了通知权限

3. **脚本执行缓慢**
   - 减少sleep()调用时间
   - 避免复杂的循环操作
   - 简化脚本逻辑

### 调试方法
- 使用`console.log()`输出调试信息
- 查看应用日志了解详细错误
- 使用简单脚本测试基本功能

## 更新日志

### v1.0.0 (当前版本)
- 实现基本JavaScript执行功能
- 提供简单的用户界面
- 支持基本的设备信息获取
- 包含完整的测试脚本

## 联系和支持

如有问题或建议，请通过以下方式联系：
- 查看项目文档
- 提交Issue报告问题
- 参考测试脚本了解用法
