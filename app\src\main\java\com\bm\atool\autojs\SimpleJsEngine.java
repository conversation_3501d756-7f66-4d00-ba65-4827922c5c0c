package com.bm.atool.autojs;

import android.content.Context;
import android.os.Handler;
import android.os.Looper;
import android.util.Log;
import android.webkit.JavascriptInterface;
import android.webkit.WebView;
import android.widget.Toast;
import org.json.JSONObject;


/**
 * 简化的JavaScript执行引擎
 * 使用WebView作为JS运行环境，提供基本的自动化功能
 */
public class SimpleJsEngine {
    private static final String TAG = "SimpleJsEngine";

    private Context context;
    private WebView webView;
    private Handler mainHandler;
    private JsExecutionCallback callback;

    public interface JsExecutionCallback {
        void onStart(String scriptName);
        void onSuccess(String scriptName, String result);
        void onError(String scriptName, String error);
    }

    public SimpleJsEngine(Context context) {
        this.context = context;
        this.mainHandler = new Handler(Looper.getMainLooper());
        initWebView();
    }

    private void initWebView() {
        mainHandler.post(() -> {
            try {
                webView = new WebView(context);
                webView.getSettings().setJavaScriptEnabled(true);
                webView.getSettings().setDomStorageEnabled(true);

                // 添加JavaScript接口
                webView.addJavascriptInterface(new JsInterface(), "Android");

                // 加载基础HTML
                String html = "<!DOCTYPE html><html><head><meta charset='utf-8'></head><body>" +
                        "<script>" +
                        "// 重写console.log以输出到Android日志" +
                        "console.log = function(msg) { " +
                        "  try { Android.log(String(msg)); } catch(e) { /* noop */ }" +
                        "};" +
                        "// 简单的console.warn/console.error实现" +
                        "console.warn = function(msg) { try { Android.log('WARN: ' + String(msg)); } catch(e) { /* noop */ } };" +
                        "console.error = function(msg) { try { Android.log('ERROR: ' + String(msg)); } catch(e) { /* noop */ } };" +
                        "// Toast显示函数" +
                        "window.toast = function(msg) { " +
                        "  try { Android.toast(String(msg)); } catch(e) { console.error('Toast error: ' + e); }" +
                        "};" +
                        "// 全局toast函数" +
                        "var toast = window.toast;" +
                        "// 延时函数（忙等待，不建议长时间阻塞）" +
                        "window.sleep = function(ms) { " +
                        "  var start = Date.now();" +
                        "  while (Date.now() - start < ms) {" +
                        "    if ((Date.now() - start) % 100 === 0) {" +
                        "      setTimeout(function(){}, 1);" +
                        "    }" +
                        "  }" +
                        "};" +
                        "// 全局sleep函数" +
                        "var sleep = window.sleep;" +
                        "// 设备信息对象" +
                        "window.device = {" +
                        "  width: window.screen ? window.screen.width : 1080," +
                        "  height: window.screen ? window.screen.height : 1920," +
                        "  brand: 'Android'," +
                        "  model: 'Unknown'" +
                        "};" +
                        "// 全局device对象" +
                        "var device = window.device;" +
                        "// 自动化服务状态" +
                        "window.auto = {" +
                        "  service: false" +
                        "};" +
                        "// 全局auto对象" +
                        "var auto = window.auto;" +
                        "// 获取当前应用包名" +
                        "window.currentPackage = function() { " +
                        "  try { return Android.getCurrentPackage(); } catch(e) { console.error('CurrentPackage error: ' + e); return 'unknown'; }" +
                        "};" +
                        "// 全局currentPackage函数" +
                        "var currentPackage = window.currentPackage;" +
                        "// 全局错误捕获" +
                        "window.onerror = function(message, source, lineno, colno, error) { " +
                        "  try { Android.onScriptError('Global', String(message)); } catch(e) { /* noop */ } " +
                        "  return true;" +
                        "};" +
                        "// 初始化完成标志" +
                        "window.autoJsReady = true;" +
                        "console.log('AutoJs API initialized successfully');" +
                        "</script>" +
                        "</body></html>";

                webView.loadDataWithBaseURL("file:///android_asset/", html, "text/html", "utf-8", null);

                Log.d(TAG, "WebView initialized successfully");
            } catch (Exception e) {
                Log.e(TAG, "Error initializing WebView", e);
            }
        });
    }

    /**
     * 执行JavaScript脚本
     */
    public void executeScript(String scriptContent, String scriptName, JsExecutionCallback callback) {
        this.callback = callback;

        if (callback != null) {
            callback.onStart(scriptName);
        }

        mainHandler.post(() -> {
            try {
                if (webView == null) {
                    if (callback != null) {
                        callback.onError(scriptName, "WebView not initialized");
                    }
                    return;
                }

                // 包装脚本以捕获错误和结果，确保API已加载
                // 使用 JSONObject.quote 安全地转义用户脚本，避免字符串拼接导致的语法错误
                final String scriptJson = JSONObject.quote(scriptContent);
                String wrappedScript =
                    "setTimeout(function() {" +
                    "  try {" +
                    "    // 检查API是否已加载" +
                    "    if (!window.autoJsReady) {" +
                    "      throw new Error('AutoJs API not ready');" +
                    "    }" +
                    "    // 先尝试编译用户脚本，捕获语法错误" +
                    "    var __userFn;" +
                    "    try {" +
                    "      var __code = " + scriptJson + ";" +
                    "      __userFn = new Function(__code);" +
                    "    } catch (compileErr) {" +
                    "      throw compileErr;" +
                    "    }" +
                    "    // 执行用户脚本" +
                    "    var result = __userFn.call(null);" +
                    "    Android.onScriptSuccess('" + scriptName + "', String(result || 'Script completed'));" +
                    "  } catch (e) {" +
                    "    Android.onScriptError('" + scriptName + "', String(e && e.message ? e.message : e));" +
                    "  }" +
                    "}, 200);"; // 稍微增加等待时间，确保API加载完成

                webView.evaluateJavascript(wrappedScript, null);

            } catch (Exception e) {
                Log.e(TAG, "Error executing script: " + scriptName, e);
                if (callback != null) {
                    callback.onError(scriptName, e.getMessage());
                }
            }
        });
    }

    /**
     * 停止脚本执行
     */
    public void stopScript() {
        mainHandler.post(() -> {
            try {
                if (webView != null) {
                    webView.evaluateJavascript("window.stop();", null);
                }
            } catch (Exception e) {
                Log.e(TAG, "Error stopping script", e);
            }
        });
    }

    /**
     * 释放资源
     */
    public void release() {
        mainHandler.post(() -> {
            try {
                if (webView != null) {
                    webView.destroy();
                    webView = null;
                }
            } catch (Exception e) {
                Log.e(TAG, "Error releasing WebView", e);
            }
        });
    }

    /**
     * JavaScript接口类
     */
    private class JsInterface {

        @JavascriptInterface
        public void log(String message) {
            Log.d(TAG, "JS Log: " + message);
        }

        @JavascriptInterface
        public void toast(String message) {
            mainHandler.post(() -> {
                try {
                    Toast.makeText(context, message, Toast.LENGTH_SHORT).show();
                } catch (Exception e) {
                    Log.e(TAG, "Error showing toast", e);
                }
            });
        }

        @JavascriptInterface
        public String getCurrentPackage() {
            try {
                // 这里可以返回当前应用的包名
                return context.getPackageName();
            } catch (Exception e) {
                Log.e(TAG, "Error getting current package", e);
                return "unknown";
            }
        }

        @JavascriptInterface
        public void onScriptSuccess(String scriptName, String result) {
            mainHandler.post(() -> {
                if (callback != null) {
                    callback.onSuccess(scriptName, result);
                }
            });
        }

        @JavascriptInterface
        public void onScriptError(String scriptName, String error) {
            mainHandler.post(() -> {
                if (callback != null) {
                    callback.onError(scriptName, error);
                }
            });
        }
    }
}
