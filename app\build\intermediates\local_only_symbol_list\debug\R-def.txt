R_DEF: Internal format may change without notice
local
array reply_entries
array reply_values
color accent_color
color app_background_color
color black
color button_primary
color check_mark_color
color colorAccent
color colorPrimary
color colorPrimaryDark
color colorSecondary
color green
color header_background
color surface_background
color text_primary
color text_secondary
color white
dimen fab_margin
drawable debug
drawable debug_select
drawable home
drawable home_select
drawable ic_action_home
drawable ic_action_ok
drawable ic_code
drawable ic_launcher_background
drawable ic_launcher_foreground
drawable rounded_background
drawable rounded_background_light
drawable setting
drawable setting_select
drawable tab_icon_debug
drawable tab_icon_home
drawable tab_icon_settings
id btnCheckPermissions
id btnCheckStatus
id btnExecuteScript
id btnExit
id btnGoSetting
id btnLogout
id btnStopAllScripts
id btnStopSocket
id btn_Login
id debugTitle
id edtPhoneNumber
id etScriptContent
id etScriptName
id et_account
id et_password
id iconLogo
id imageView
id imgStatus
id layoutIcon
id layoutUserInfo
id permissionsListView
id phoneListView
id smsListView
id tab_layout
id tvRunningScripts
id tvScriptStatus
id txtContent
id txtDisplayName
id txtFrom
id txtName
id txtSlot
id txtStatus
id txtSubscriptionId
id txtTime
id txtUserName
id view_pager
layout activity_float_item
layout activity_login
layout activity_main
layout fragment_autojs
layout fragment_debug
layout fragment_main
layout fragment_settings
layout fragment_simple_autojs
layout phone_row
layout setting_row
layout sms_row
mipmap ic_launcher
mipmap ic_launcher_round
raw sliant
string action_settings
string app_name
string attachment_summary_off
string attachment_summary_on
string attachment_title
string autojs_accessibility_service_description
string first_fragment_label
string lorem_ipsum
string messages_header
string next
string previous
string reply_title
string second_fragment_label
string signature_title
string sync_header
string sync_title
string title_activity_settings
style Base.Theme.AndroidTool
style Base.Theme.Snettool
style Theme.AndroidTool
style Theme.Snettool
xml allocation
xml autojs_accessibility_service
xml backup_rules
xml data_extraction_rules
